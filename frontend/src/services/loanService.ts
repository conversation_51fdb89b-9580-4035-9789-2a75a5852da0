import { apiClient } from '../utils/api';
import { API_ENDPOINTS } from '../utils/constants';
import { PaginatedResponse, PaginationParams } from '../types';

export interface LoanApplication {
  id: string;
  memberId: string;
  memberName: string;
  memberMemberId: string;
  loanAmount: number;
  purpose: string;
  guarantorId?: string;
  guarantorName?: string;
  status: 'pending' | 'approved' | 'rejected' | 'disbursed';
  appliedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewerName?: string;
  rejectionReason?: string;
  branchId: string;
  branchName?: string;
  createdBy: string;
  creatorName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateLoanApplicationData {
  memberId: string;
  loanAmount: number;
  purpose: string;
  guarantorId?: string;
}

export interface UpdateLoanApplicationData {
  loanAmount?: number;
  purpose?: string;
  guarantorId?: string;
}

export interface LoanApplicationListQuery extends PaginationParams {
  search?: string;
  status?: LoanApplication['status'];
  branchId?: string;
  memberId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface Loan {
  id: string;
  applicationId: string;
  memberId: string;
  memberName: string;
  memberMemberId: string;
  loanAmount: number;
  interestRate: number;
  installmentAmount: number;
  totalInstallments: number;
  paidInstallments: number;
  remainingAmount: number;
  status: 'active' | 'completed' | 'defaulted';
  disbursedAt: string;
  nextInstallmentDate: string;
  branchId: string;
  branchName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoanListQuery extends PaginationParams {
  search?: string;
  status?: Loan['status'];
  branchId?: string;
  memberId?: string;
  overdueOnly?: boolean;
}

export interface LoanInstallment {
  id: string;
  loanId: string;
  installmentNo: number;
  amount: number;
  dueDate: string;
  paidDate?: string;
  paidAmount?: number;
  status: 'pending' | 'paid' | 'overdue' | 'partial';
  lateFee?: number;
  createdAt: string;
  updatedAt: string;
}

export interface ApprovalData {
  approvedAmount: number;
  interestRate?: number;
  installmentAmount?: number;
  totalInstallments?: number;
  disbursementDate?: string;
  firstInstallmentDate?: string;
  notes?: string;
}

export interface RejectionData {
  rejectionReason: string;
  notes?: string;
}

export class LoanService {
  // Loan Applications
  async getLoanApplications(query: LoanApplicationListQuery = {}): Promise<PaginatedResponse<LoanApplication>> {
    const response = await apiClient.get<PaginatedResponse<LoanApplication>>(
      API_ENDPOINTS.LOANS.APPLICATIONS,
      {
        params: query,
        cache: { ttl: 30000 }, // Cache for 30 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch loan applications');
  }

  async getLoanApplicationById(id: string): Promise<LoanApplication> {
    const response = await apiClient.get<LoanApplication>(
      API_ENDPOINTS.LOANS.GET_APPLICATION(id),
      {
        cache: { ttl: 60000 }, // Cache for 1 minute
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch loan application');
  }

  async createLoanApplication(data: CreateLoanApplicationData): Promise<LoanApplication> {
    const response = await apiClient.post<LoanApplication>(
      API_ENDPOINTS.LOANS.CREATE_APPLICATION,
      data
    );

    if (response.success && response.data) {
      // Clear applications cache
      apiClient.removeCacheEntry(API_ENDPOINTS.LOANS.APPLICATIONS);
      return response.data;
    }

    throw new Error(response.error || 'Failed to create loan application');
  }

  async updateLoanApplication(id: string, data: UpdateLoanApplicationData): Promise<LoanApplication> {
    const response = await apiClient.put<LoanApplication>(
      API_ENDPOINTS.LOANS.UPDATE_APPLICATION(id),
      data
    );

    if (response.success && response.data) {
      // Clear relevant caches
      apiClient.removeCacheEntry(API_ENDPOINTS.LOANS.APPLICATIONS);
      apiClient.removeCacheEntry(API_ENDPOINTS.LOANS.GET_APPLICATION(id));
      return response.data;
    }

    throw new Error(response.error || 'Failed to update loan application');
  }

  async deleteLoanApplication(id: string): Promise<void> {
    const response = await apiClient.delete(API_ENDPOINTS.LOANS.DELETE_APPLICATION(id));

    if (!response.success) {
      throw new Error(response.error || 'Failed to delete loan application');
    }

    // Clear relevant caches
    apiClient.removeCacheEntry(API_ENDPOINTS.LOANS.APPLICATIONS);
    apiClient.removeCacheEntry(API_ENDPOINTS.LOANS.GET_APPLICATION(id));
  }

  async approveLoanApplication(id: string, approvalData: ApprovalData): Promise<Loan> {
    const response = await apiClient.post<Loan>(
      API_ENDPOINTS.LOANS.APPROVE_APPLICATION(id),
      approvalData
    );

    if (response.success && response.data) {
      // Clear relevant caches
      apiClient.removeCacheEntry(API_ENDPOINTS.LOANS.APPLICATIONS);
      apiClient.removeCacheEntry(API_ENDPOINTS.LOANS.GET_APPLICATION(id));
      apiClient.removeCacheEntry(API_ENDPOINTS.LOANS.LIST);
      return response.data;
    }

    throw new Error(response.error || 'Failed to approve loan application');
  }

  async rejectLoanApplication(id: string, rejectionData: RejectionData): Promise<void> {
    const response = await apiClient.post(
      API_ENDPOINTS.LOANS.REJECT_APPLICATION(id),
      rejectionData
    );

    if (!response.success) {
      throw new Error(response.error || 'Failed to reject loan application');
    }

    // Clear relevant caches
    apiClient.removeCacheEntry(API_ENDPOINTS.LOANS.APPLICATIONS);
    apiClient.removeCacheEntry(API_ENDPOINTS.LOANS.GET_APPLICATION(id));
  }

  // Active Loans
  async getLoans(query: LoanListQuery = {}): Promise<PaginatedResponse<Loan>> {
    const response = await apiClient.get<PaginatedResponse<Loan>>(
      API_ENDPOINTS.LOANS.LIST,
      {
        params: query,
        cache: { ttl: 30000 }, // Cache for 30 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch loans');
  }

  async getLoanById(id: string): Promise<Loan> {
    const response = await apiClient.get<Loan>(
      API_ENDPOINTS.LOANS.GET(id),
      {
        cache: { ttl: 60000 }, // Cache for 1 minute
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch loan');
  }

  async getLoanInstallments(loanId: string): Promise<LoanInstallment[]> {
    const response = await apiClient.get<LoanInstallment[]>(
      API_ENDPOINTS.LOANS.INSTALLMENTS(loanId),
      {
        cache: { ttl: 30000 }, // Cache for 30 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch loan installments');
  }

  // Loan Calculator
  async calculateLoan(data: LoanCalculationData): Promise<LoanCalculationResult> {
    const response = await apiClient.post<LoanCalculationResult>(
      API_ENDPOINTS.LOANS.CALCULATE,
      data
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to calculate loan');
  }

  // Utility methods
  async getPendingApplications(branchId?: string): Promise<LoanApplication[]> {
    const query: LoanApplicationListQuery = { status: 'pending', limit: 100 };
    if (branchId) query.branchId = branchId;
    
    const result = await this.getLoanApplications(query);
    return result.data;
  }

  async getActiveLoans(branchId?: string): Promise<Loan[]> {
    const query: LoanListQuery = { status: 'active', limit: 100 };
    if (branchId) query.branchId = branchId;
    
    const result = await this.getLoans(query);
    return result.data;
  }

  async getOverdueLoans(branchId?: string): Promise<Loan[]> {
    const query: LoanListQuery = { overdueOnly: true, limit: 100 };
    if (branchId) query.branchId = branchId;
    
    const result = await this.getLoans(query);
    return result.data;
  }

  async getMemberLoans(memberId: string): Promise<Loan[]> {
    const result = await this.getLoans({ memberId, limit: 100 });
    return result.data;
  }
}

export const loanService = new LoanService();
