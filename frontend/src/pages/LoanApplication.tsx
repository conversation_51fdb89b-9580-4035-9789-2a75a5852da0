import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Form,
  FormGroup,
  Input,
  Select,
  Textarea,
  Button,
  Alert
} from '../components/ui';
import { MemberSearch } from '../components/members';
import { CreateLoanApplicationData, MemberSearchResult, Branch } from '../types';
import { useAuth } from '../contexts/AuthContext';
import LoanService from '../services/loanService';
import BranchService from '../services/branchService';

export const LoanApplication: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedMember, setSelectedMember] = useState<MemberSearchResult | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loanCalculation, setLoanCalculation] = useState<any>(null);
  const [showCalculator, setShowCalculator] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<CreateLoanApplicationData>({
    defaultValues: {
      loanCycleNumber: 1,
      advancePayment: 0
    }
  });

  const watchedAmount = watch('appliedAmount');
  const watchedAdvancePayment = watch('advancePayment');

  useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await BranchService.getBranches();
        setBranches(response.data);
      } catch (err) {
        console.error('Error fetching branches:', err);
      }
    };

    fetchBranches();
  }, []);

  // Calculate loan details when amount or advance payment changes
  useEffect(() => {
    if (watchedAmount && watchedAmount > 0) {
      calculateLoan();
    }
  }, [watchedAmount, watchedAdvancePayment]);

  const calculateLoan = async () => {
    if (!watchedAmount || watchedAmount < 1000) return;

    try {
      const calculationData = {
        loanAmount: Number(watchedAmount),
        repaymentDuration: 12, // Default 12 months
        repaymentMethod: 'monthly' as const,
        advancePayment: Number(watchedAdvancePayment) || 0
      };

      const result = await LoanService.calculateLoan(calculationData);
      setLoanCalculation(result);
    } catch (error) {
      console.error('Error calculating loan:', error);
    }
  };

  const onSubmit = async (data: CreateLoanApplicationData) => {
    if (!selectedMember) {
      setError('Please select a member for the loan application.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const applicationData = {
        ...data,
        memberId: selectedMember.id,
        appliedAmount: Number(data.appliedAmount),
        loanCycleNumber: Number(data.loanCycleNumber),
        advancePayment: data.advancePayment ? Number(data.advancePayment) : undefined
      };

      await LoanService.createLoanApplication(applicationData);
      setSuccess('Loan application submitted successfully!');

      setTimeout(() => {
        navigate('/dashboard', {
          state: {
            message: 'Loan application submitted successfully!',
            type: 'success'
          }
        });
      }, 2000);
    } catch (err: any) {
      console.error('Error creating loan application:', err);
      setError(err.response?.data?.message || 'Failed to submit loan application. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  const handleMemberSelect = (member: MemberSearchResult) => {
    setSelectedMember(member);
    setError(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Loan Application
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Submit a new loan application
          </p>
        </div>
        <button
          onClick={handleCancel}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          ← Back to Dashboard
        </button>
      </div>

      {/* Success Alert */}
      {success && (
        <Alert 
          type="success" 
          title="Success" 
          description={success}
        />
      )}

      {/* Error Alert */}
      {error && (
        <Alert 
          type="error" 
          title="Error" 
          description={error}
          onClose={() => setError(null)}
        />
      )}

      {/* Application Form */}
      <Card>
        <CardHeader>
          <CardTitle>Loan Application Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form onSubmit={handleSubmit(onSubmit)} spacing="lg">
            {/* Member Selection */}
            <FormGroup>
              <MemberSearch
                label="Select Member *"
                value={selectedMember}
                onChange={handleMemberSelect}
                placeholder="Search for member by name or ID..."
                branchId={user?.branchId}
                required
              />
              {selectedMember && (
                <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-sm text-green-700 dark:text-green-300">
                    Selected: {selectedMember.name} (ID: {selectedMember.memberId})
                  </p>
                </div>
              )}
            </FormGroup>

            {/* Loan Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Applied Amount (৳)"
                type="number"
                step="100"
                min="1000"
                {...register('appliedAmount', {
                  required: 'Applied amount is required',
                  min: { value: 1000, message: 'Minimum loan amount is ৳1,000' },
                  max: { value: 1000000, message: 'Maximum loan amount is ৳10,00,000' }
                })}
                error={errors.appliedAmount?.message}
                required
              />

              <Input
                label="Loan Cycle Number"
                type="number"
                min="1"
                {...register('loanCycleNumber', {
                  required: 'Loan cycle number is required',
                  min: { value: 1, message: 'Loan cycle number must be at least 1' }
                })}
                error={errors.loanCycleNumber?.message}
                required
              />
            </div>

            {/* Loan Reason */}
            <Textarea
              label="Reason for Loan"
              rows={3}
              {...register('reason', {
                required: 'Reason is required',
                minLength: { value: 10, message: 'Reason must be at least 10 characters' }
              })}
              error={errors.reason?.message}
              placeholder="Describe the purpose of the loan in detail..."
              required
            />

            {/* Recommender and Advance Payment */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Recommender"
                {...register('recommender', {
                  required: 'Recommender is required'
                })}
                error={errors.recommender?.message}
                placeholder="Name of the recommender"
                required
              />

              <Input
                label="Advance Payment (৳)"
                type="number"
                step="100"
                min="0"
                {...register('advancePayment', {
                  min: { value: 0, message: 'Advance payment cannot be negative' }
                })}
                error={errors.advancePayment?.message}
                placeholder="Optional advance payment amount"
              />
            </div>

            {/* Loan Calculator Integration */}
            {loanCalculation && (
              <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                    <span>🧮</span>
                    Loan Calculation Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Loan Amount</p>
                      <p className="font-semibold">৳{loanCalculation.loanAmount?.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Total Repayment</p>
                      <p className="font-semibold">৳{loanCalculation.totalRepaymentAmount?.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Advance Payment</p>
                      <p className="font-semibold">৳{loanCalculation.advancePayment?.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Monthly Installment</p>
                      <p className="font-semibold">৳{loanCalculation.installmentAmount?.toLocaleString()}</p>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t border-blue-200 dark:border-blue-700">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {loanCalculation.installmentCount} installments over {Math.floor(loanCalculation.installmentCount / 12)} year(s)
                      </span>
                      <Button
                        type="button"
                        variant="secondary"
                        size="sm"
                        onClick={() => navigate('/calculator', {
                          state: {
                            loanAmount: watchedAmount,
                            advancePayment: watchedAdvancePayment
                          }
                        })}
                      >
                        Open Calculator
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="submit"
                variant="primary"
                disabled={loading || !selectedMember}
                className="flex-1 md:flex-none"
              >
                {loading ? 'Submitting...' : 'Submit Application'}
              </Button>

              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate('/calculator', {
                  state: {
                    loanAmount: watchedAmount,
                    advancePayment: watchedAdvancePayment
                  }
                })}
                disabled={!watchedAmount || watchedAmount < 1000}
                className="flex-1 md:flex-none"
              >
                Calculate Loan
              </Button>

              <Button
                type="button"
                variant="secondary"
                onClick={handleCancel}
                disabled={loading}
              >
                Cancel
              </Button>

              <Button
                type="button"
                variant="secondary"
                onClick={() => {
                  reset();
                  setSelectedMember(null);
                  setLoanCalculation(null);
                  setError(null);
                  setSuccess(null);
                }}
                disabled={loading}
              >
                Reset
              </Button>
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default LoanApplication;
